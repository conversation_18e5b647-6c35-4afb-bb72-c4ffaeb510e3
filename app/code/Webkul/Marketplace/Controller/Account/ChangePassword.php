<?php
declare(strict_types=1);

namespace Webkul\Marketplace\Controller\Account;

use Magento\Framework\App\Action\Action;
use Magento\Framework\App\Action\Context;
use Magento\Customer\Api\AccountManagementInterface;
use Magento\Customer\Model\Session as CustomerSession;
use Magento\Framework\Exception\NoSuchEntityException;
use Magento\Framework\Exception\InvalidEmailOrPasswordException;
use Magento\Framework\Exception\LocalizedException;
use Magento\Framework\App\Action\HttpPostActionInterface;
use Magento\Framework\Data\Form\FormKey\Validator as FormKeyValidator;
use Magento\Framework\App\RequestInterface;
use Magento\Customer\Model\Url as CustomerUrl;
use Webkul\Marketplace\Helper\Data as MarketplaceHelper;

/**
 * Seller change password controller
 *
 * Dedicated controller for handling password changes in marketplace seller dashboard.
 * Follows the same patterns as EditprofilePost but specifically for password management.
 */
class ChangePassword extends Action implements HttpPostActionInterface
{
    /**
     * @var CustomerSession
     */
    protected $customerSession;

    /**
     * @var FormKeyValidator
     */
    protected $formKeyValidator;

    /**
     * @var AccountManagementInterface
     */
    protected $accountManagement;

    /**
     * @var MarketplaceHelper
     */
    protected $marketplaceHelper;

    /**
     * @var CustomerUrl
     */
    protected $customerUrl;

    /**
     * @param Context $context
     * @param CustomerSession $customerSession
     * @param FormKeyValidator $formKeyValidator
     * @param AccountManagementInterface $accountManagement
     * @param MarketplaceHelper $marketplaceHelper
     * @param CustomerUrl $customerUrl
     */
    public function __construct(
        Context $context,
        CustomerSession $customerSession,
        FormKeyValidator $formKeyValidator,
        AccountManagementInterface $accountManagement,
        MarketplaceHelper $marketplaceHelper,
        CustomerUrl $customerUrl
    ) {
        $this->customerSession = $customerSession;
        $this->formKeyValidator = $formKeyValidator;
        $this->accountManagement = $accountManagement;
        $this->marketplaceHelper = $marketplaceHelper;
        $this->customerUrl = $customerUrl;
        parent::__construct($context);
    }

    /**
     * Check customer authentication.
     *
     * @param RequestInterface $request
     * @return \Magento\Framework\App\ResponseInterface
     */
    public function dispatch(RequestInterface $request)
    {
        $loginUrl = $this->customerUrl->getLoginUrl();

        if (!$this->customerSession->authenticate($loginUrl)) {
            $this->_actionFlag->set('', self::FLAG_NO_DISPATCH, true);
        }

        return parent::dispatch($request);
    }

    /**
     * Execute password change
     *
     * @return \Magento\Framework\Controller\Result\RedirectFactory
     */
    public function execute()
    {
        if (!$this->getRequest()->isPost()) {
            return $this->resultRedirectFactory->create()->setPath(
                'marketplace/account/editProfile',
                ['_secure' => $this->getRequest()->isSecure()]
            );
        }

        if (!$this->formKeyValidator->validate($this->getRequest())) {
            $this->messageManager->addErrorMessage(__('Invalid form key. Please try again.'));
            return $this->resultRedirectFactory->create()->setPath(
                'marketplace/account/editProfile',
                ['_secure' => $this->getRequest()->isSecure()]
            );
        }

        $fields = $this->getRequest()->getParams();
        $errors = $this->validatePasswordData($fields);

        if (empty($errors)) {
            $customerId = (int) $this->customerSession->getId();
            $this->handlePasswordChange($customerId, $fields);
        } else {
            foreach ($errors as $message) {
                $this->messageManager->addError($message);
            }
        }

        return $this->resultRedirectFactory->create()->setPath(
            'marketplace/account/editProfile',
            ['_secure' => $this->getRequest()->isSecure()]
        );
    }

    /**
     * Validate password data
     *
     * @param array $fields
     * @return array
     */
    protected function validatePasswordData($fields)
    {
        $errors = [];

        $currentPassword = $fields['current_password'] ?? '';
        $newPassword = $fields['new_password'] ?? '';
        $confirmPassword = $fields['confirm_password'] ?? '';

        if (empty($currentPassword)) {
            $errors[] = __('Current password is required.');
        }

        if (empty($newPassword)) {
            $errors[] = __('New password is required.');
        }

        if (empty($confirmPassword)) {
            $errors[] = __('Password confirmation is required.');
        }

        if (!empty($newPassword) && !empty($confirmPassword) && $newPassword !== $confirmPassword) {
            $errors[] = __('New password and confirmation do not match.');
        }

        if (!empty($newPassword) && strlen($newPassword) < 8) {
            $errors[] = __('Password must be at least 8 characters long.');
        }

        return $errors;
    }

    /**
     * Handle password change
     *
     * @param int $customerId
     * @param array $fields
     * @return void
     */
    protected function handlePasswordChange($customerId, $fields)
    {
        $currentPassword = $fields['current_password'];
        $newPassword = $fields['new_password'];

        try {
            $this->accountManagement->changePasswordById($customerId, $currentPassword, $newPassword);
            $this->marketplaceHelper->logDataInLogger(
                "Seller password changed successfully for customer ID: " . $customerId
            );

            $this->messageManager->addSuccessMessage(__('Your password has been successfully changed.'));

        } catch (InvalidEmailOrPasswordException $e) {
            $this->messageManager->addErrorMessage(__('The current password you entered is incorrect.'));
        } catch (NoSuchEntityException $e) {
            $this->messageManager->addErrorMessage(__('Unable to find your customer account.'));
        } catch (LocalizedException $e) {
            $this->messageManager->addErrorMessage($e->getMessage());
        } catch (\Exception $e) {
            $this->marketplaceHelper->logDataInLogger(
                "Password change error for customer ID " . $customerId . ": " . $e->getMessage()
            );
            $this->messageManager->addErrorMessage(__('Something went wrong while changing your password. Please try again.'));
        }
    }
}
